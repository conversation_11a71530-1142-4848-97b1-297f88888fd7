import asyncio
import logging
import os
import re
import time
import traceback
from datetime import UTC, datetime, timezone
from typing import Any, Optional, Union

from pydantic import BaseModel, Field

from .backends.base import (
    C<PERSON>lerBackend,
)

# Rename to avoid clash
from .backends.base import (
    CrawlResult as BackendCrawlResult,
)

# from .backends.http import HTTPBackend, HTTPBackendConfig # http.py is deleted
from .backends.http_backend import HTTPBackend, HTTPBackendConfig  # Use http_backend.py
from .backends.selector import BackendCriteria, BackendSelector
from .organizers.doc_organizer import DocumentOrganizer
from .processors.content_processor import ContentProcessor, ProcessedContent
# Alias these imports
from .processors.quality_checker import (
    IssueLevel as PQ_IssueLevel,
    IssueType as PQ_IssueType,
    QualityChecker as PQ_QualityChecker,
    QualityIssue as PQ_QualityIssue,
)

# ProjectIdentifier, ProjectType, ProjectIdentity moved to src.utils.project_identifier
from .utils.project_identifier import ProjectIdentifier
from .utils.search import DuckDuckGoSearch  # Import from new location
from .utils.url.factory import create_url_info  # Added import for factory
from .utils.helpers import RateLimiter, RetryStrategy  # Added import

# Import the correct URLInfo and sanitize function
from .utils.url.info import URLInfo  # Corrected import path for modular URLInfo


class CrawlTarget(BaseModel):
    """Model for crawl target configuration."""

    url: str = Field(default="https://docs.python.org/3/")  # Default to Python docs
    depth: int = Field(default=1)
    follow_external: bool = Field(default=False)
    content_types: list[str] = Field(default=["text/html"])
    exclude_patterns: list[str] = Field(default_factory=list)
    include_patterns: list[str] = Field(default_factory=list)
    required_patterns: list[str] = Field(
        default_factory=list
    )  # Changed from ["/3/"] to empty list
    metadata: dict[str, Any] = Field(default_factory=dict)
    max_pages: Optional[int] = Field(
        default=1000
    )  # Changed from None to 1000 to match test expectations
    allowed_paths: list[str] = Field(default_factory=list)
    excluded_paths: list[str] = Field(default_factory=list)


class CrawlStats(BaseModel):
    """Model for crawl statistics."""

    start_time: datetime = Field(default_factory=lambda: datetime.now(UTC))
    end_time: Optional[datetime] = None
    pages_crawled: int = 0
    successful_crawls: int = 0
    failed_crawls: int = 0
    skipped_pages: int = 0
    total_time: float = 0.0
    average_time_per_page: float = 0.0
    quality_issues: int = 0
    bytes_processed: int = 0
    errors: int = 0


class CrawlResult(BaseModel):
    """Model for crawl results."""

    target: CrawlTarget
    stats: CrawlStats
    documents: list[dict[str, Any]]
    issues: list[PQ_QualityIssue] # Use aliased QualityIssue
    metrics: dict[str, Any]
    structure: Optional[list[dict[str, Any]]] = None
    processed_url: Optional[str] = (
        None  # Added to store the final URL processed (after redirects, normalization)
    )
    failed_urls: list[dict[str, str]] = Field(
        default_factory=list
    )  # List of URLs that failed to crawl
    errors: dict[str, Exception] = Field(
        default_factory=dict
    )  # Map of URLs to their exceptions
    crawled_pages: dict[str, Any] = Field(
        default_factory=dict
    )  # Dictionary of crawled pages
    crawled_urls: set[str] = Field(
        default_factory=set
    )  # Set of normalized URLs that were crawled
    project_identity: Optional[Any] = None

    model_config = {
        "arbitrary_types_allowed": True  # Allow arbitrary types like Exception
    }


class CrawlerConfig(BaseModel):
    """Configuration for the crawler."""

    concurrent_requests: int = 10
    requests_per_second: float = 5.0
    max_retries: int = 3
    request_timeout: float = 30.0
    respect_robots_txt: bool = True
    follow_redirects: bool = True
    verify_ssl: bool = True
    user_agent: str = "Python Documentation Scraper/1.0"
    headers: dict[str, str] = Field(default_factory=dict)
    use_duckduckgo: bool = True
    duckduckgo_max_results: int = 10
    batch_size: int = (
        5  # Kept for potential future use, but not used in current crawl logic
    )
    # Add missing attributes that the new implementation expects
    max_async_tasks: int = 10  # Alias for concurrent_requests
    rate_limit: float = 0.2  # seconds between requests (1/requests_per_second)
    max_depth: int = 3  # Maximum crawl depth
    max_pages: int = 1000  # Maximum pages to crawl


class DocumentationCrawler:
    """Main crawler orchestrator."""

    def __init__(
        self,
        config: Optional[CrawlerConfig] = None,
        backend_selector: Optional[BackendSelector] = None,
        content_processor: Optional[ContentProcessor] = None,
        quality_checker: Optional[PQ_QualityChecker] = None, # Use aliased PQ_QualityChecker
        document_organizer: Optional[DocumentOrganizer] = None,
        loop: Optional[Any] = None, # Restored Any type
        backend: Optional[CrawlerBackend] = None, # Corrected syntax: Optional[CrawlerBackend]
    ) -> None:
        """Initialize the documentation crawler.

        Args:
            config: Optional crawler configuration
            backend_selector: Optional backend selector for choosing appropriate backends
            content_processor: Optional content processor for processing crawled content
            quality_checker: Optional quality checker for checking content quality
            document_organizer: Optional document organizer for organizing crawled content
            loop: Optional event loop for asyncio
            backend: Optional specific backend to use instead of using the selector
        """
        self.config = config or CrawlerConfig()
        logging.debug(
            f"Crawler __init__ received backend_selector: id={id(backend_selector)}"
        )
        # Optional event loop passed for testing
        self.loop = loop
        if backend_selector:
            logging.debug(
                f"Received selector initial backends: {list(backend_selector._backends.keys())}"
            )
        self.backend_selector = backend_selector or BackendSelector()
        self.content_processor = content_processor or ContentProcessor()
        self.quality_checker = quality_checker or PQ_QualityChecker() # Use aliased PQ_QualityChecker
        self.document_organizer = document_organizer or DocumentOrganizer()

        # If a specific backend is provided, use it directly
        self.direct_backend = backend
        # Also set self.backend for direct access in tests
        self.backend = backend
        logging.debug(f"Direct backend provided: {backend is not None}")

        logging.debug(
            f"Crawler assigned self.backend_selector: id={id(self.backend_selector)}"
        )
        logging.debug(
            f"Assigned selector backends BEFORE http registration: {list(self.backend_selector._backends.keys())}"
        )

        # Set up HTTP backend
        http_backend = HTTPBackend(
            HTTPBackendConfig(
                timeout=self.config.request_timeout,
                verify_ssl=self.config.verify_ssl,
                follow_redirects=self.config.follow_redirects,
                headers={"User-Agent": self.config.user_agent},
            )
        )

        # Register HTTP backend with criteria
        self.backend_selector.register_backend(
            name=http_backend.name,  # Use the name attribute
            backend=http_backend,  # Pass the instance
            criteria=BackendCriteria(  # Pass the criteria
                priority=1,
                content_types=["text/html"],
                url_patterns=["http://", "https://"],  # Match all HTTP(S) URLs
                max_load=0.8,
                min_success_rate=0.7,
            ),
        )
        logging.debug(
            f"Assigned selector backends AFTER http registration: {list(self.backend_selector._backends.keys())}"
        )

        self.rate_limiter = RateLimiter(self.config.requests_per_second)
        self.retry_strategy = RetryStrategy(
            max_retries=self.config.max_retries
        )  # Corrected closing parenthesis

        self._crawled_urls: set[str] = set()  # Store normalized URLs
        self._processing_semaphore = asyncio.Semaphore(self.config.concurrent_requests)
        self.client_session = None  # Session managed by HTTPBackend now

        self.duckduckgo = (
            DuckDuckGoSearch(self.config.duckduckgo_max_results)
            if self.config.use_duckduckgo
            else None
        )
        self.project_identifier = ProjectIdentifier()

    def _find_links_recursive(self, structure_element) -> list[str]:
        """Recursively find all 'href' values from link elements in the structure."""
        links = []
        if isinstance(structure_element, dict):
            # Check if the current element itself is a link
            if structure_element.get("type") in [
                "link",
                "link_inline",
            ] and structure_element.get("href"):
                links.append(structure_element["href"])
            # Recursively check values that are lists or dicts
            for value in structure_element.values():
                if isinstance(value, (dict, list)):
                    links.extend(self._find_links_recursive(value))
        elif isinstance(structure_element, list):
            # Recursively check items in the list
            for item in structure_element:
                links.extend(self._find_links_recursive(item))
        return links

    def _should_follow_link(self, url_info: URLInfo, target: CrawlTarget, current_depth: int) -> bool:
        """Check if a link should be followed."""
        # Simple depth check
        if current_depth >= target.depth:
            return False
        
        # Check if URL should be crawled
        return self._should_crawl_url(url_info, target)

    def _should_crawl_url(self, url_info: URLInfo, target: CrawlTarget) -> bool:
        """Check if a URL should be crawled based on target rules."""
        if not url_info.is_valid:
            logging.debug(
                f"Skipping invalid URL: {url_info.raw_url} ({url_info.error_message})"
            )
            return False

        normalized_url = url_info.normalized_url

        # Check if already crawled
        if normalized_url in self._crawled_urls:
            logging.debug(f"Skipping already crawled URL: {normalized_url}")
            return False

        # Check scheme
        if url_info.scheme not in ["http", "https", "file"]:
            logging.debug(f"Skipping non-HTTP/S/file URL: {normalized_url}")
            return False

        # Check if external URLs should be followed
        if not target.follow_external:
            target_url_info = create_url_info(
                target.url
            )  # Base for the entire crawl operation
            logging.debug(
                f"[_should_crawl_url] target_url_info.scheme: {target_url_info.scheme} for target.url: {target.url}"
            )

            # If schemes are different, it's generally external
            if url_info.scheme != target_url_info.scheme:
                # Allow http -> https upgrade as internal for the same domain
                if not (
                    target_url_info.scheme == "http"
                    and url_info.scheme == "https"
                    and url_info.registered_domain == target_url_info.registered_domain
                ):
                    logging.debug(
                        f"Skipping URL with different scheme: {url_info.normalized_url} (target scheme: {target_url_info.scheme})"
                    )
                    return False

            # If schemes are http/https, compare registered domains
            elif url_info.scheme in ["http", "https"]:
                if url_info.registered_domain != target_url_info.registered_domain:
                    logging.debug(
                        f"Skipping external http/https domain: {url_info.normalized_url} (target domain: {target_url_info.registered_domain})"
                    )
                    return False
            # If schemes are both 'file', they are considered internal to this crawl operation
            elif url_info.scheme == "file" and target_url_info.scheme == "file":
                pass  # Internal, continue with other checks
            else:
                # Catch-all for other scheme combinations when follow_external is False
                logging.debug(
                    f"Skipping URL due to scheme/domain mismatch with follow_external=False: {url_info.normalized_url} (current scheme: {url_info.scheme}, target scheme: {target_url_info.scheme})"
                )
                return False

        # Check exclude patterns
        if any(
            re.search(pattern, normalized_url) for pattern in target.exclude_patterns
        ):
            logging.debug(f"Skipping URL due to exclude pattern: {normalized_url}")
            return False

        # Check required patterns (if any)
        if target.required_patterns and not any(
            re.search(pattern, normalized_url) for pattern in target.required_patterns
        ):
            logging.debug(
                f"Skipping URL due to missing required pattern: {normalized_url}"
            )
            return False

        # Check allowed paths (if any)
        if target.allowed_paths:
            path = url_info.path
            if not any(path.startswith(allowed) for allowed in target.allowed_paths):
                logging.debug(
                    f"Skipping URL due to not being in allowed paths: {normalized_url}"
                )
                return False

        # Check excluded paths (if any)
        if target.excluded_paths:
            path = url_info.path
            if any(path.startswith(excluded) for excluded in target.excluded_paths):
                logging.debug(
                    f"Skipping URL due to being in excluded paths: {normalized_url}"
                )
                return False

        return True

    async def _process_file_url(self, url_info: URLInfo, current_depth: int, target_cfg: CrawlTarget, stats_obj: CrawlStats):
        """Process a single file URL and return CrawlResult."""
        logging.debug(f"Processing file URL: {url_info.normalized_url} at depth {current_depth}")
        
        # Initialize local lists for collecting documents and issues
        collected_documents: list[dict[str, Any]] = []
        collected_issues: list[PQ_QualityIssue] = []
        
        new_links: list[URLInfo] = []
        metrics: dict[str, Any] = {"content_size": 0, "link_count": 0, "processing_time_ms": 0}
        start_time = time.perf_counter()

        file_path = url_info.path
        if file_path is None:
            file_path = ""  # Ensure file_path is a string

        url_str = url_info.normalized_url

        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            if os.path.isdir(file_path):
                # Attempt to find and process an index file if the path is a directory
                index_file_path = None
                for index_name in ["index.html", "index.htm"]:
                    potential_index_path = os.path.join(file_path, index_name)
                    if os.path.exists(potential_index_path) and os.path.isfile(potential_index_path):
                        index_file_path = potential_index_path
                        url_str = create_url_info(f"file://{index_file_path}").normalized_url  # Update url_str to index file
                        file_path = index_file_path  # Update file_path to index file
                        logging.info(f"Processing directory index file: {file_path}")
                        break
                if not index_file_path:
                    raise IsADirectoryError(f"Path is a directory without a recognized index file: {file_path}")

            with open(file_path, encoding="utf-8", errors="ignore") as f:
                file_content = f.read()

            metrics["content_size"] = len(file_content.encode('utf-8'))

        except FileNotFoundError as e:
            logging.warning(f"File not found for URL {url_str}: {e}")
            issue = PQ_QualityIssue(
                issue_type=PQ_IssueType.FILE_NOT_FOUND,
                level=PQ_IssueLevel.WARNING,  # Changed to WARNING as it might be an expected skip
                message=str(e),
                details={"file_path": file_path, "error": str(e)},
                url_context=url_str
            )
            collected_issues.append(issue)
            stats_obj.skipped_pages += 1  # Changed from failed_crawls

            end_time = time.perf_counter()
            metrics["processing_time_ms"] = (end_time - start_time) * 1000
            
            # Create result with issues
            result = CrawlResult(
                target=target_cfg, 
                stats=stats_obj, 
                documents=collected_documents, 
                issues=collected_issues,
                metrics=metrics,
                failed_urls=[{"url": url_str, "reason": f"FileNotFoundError: {e}"}]
            )
            
            return result, new_links, metrics

        except IsADirectoryError as e:
            logging.warning(f"Path is a directory for URL {url_str}: {e}")
            issue = PQ_QualityIssue(
                issue_type=PQ_IssueType.FILE_SYSTEM_ERROR,  # Or a more specific type
                level=PQ_IssueLevel.WARNING,
                message=str(e),
                details={"file_path": file_path, "error": str(e)},
                url_context=url_str
            )
            collected_issues.append(issue)
            stats_obj.skipped_pages += 1

            end_time = time.perf_counter()
            metrics["processing_time_ms"] = (end_time - start_time) * 1000
            
            # Create result with issues
            result = CrawlResult(
                target=target_cfg, 
                stats=stats_obj, 
                documents=collected_documents, 
                issues=collected_issues,
                metrics=metrics,
                failed_urls=[{"url": url_str, "reason": f"IsADirectoryError: {e}"}]
            )
            
            return result, new_links, metrics

        except OSError as e:
            logging.error(f"OSError processing file URL {url_str}: {e}")
            issue = PQ_QualityIssue(
                issue_type=PQ_IssueType.FILE_SYSTEM_ERROR,
                level=PQ_IssueLevel.ERROR,
                message=f"File system error for {url_str}: {e}",
                details={"file_path": file_path, "error": str(e)},
                url_context=url_str
            )
            collected_issues.append(issue)
            stats_obj.failed_crawls += 1
            stats_obj.errors += 1

            end_time = time.perf_counter()
            metrics["processing_time_ms"] = (end_time - start_time) * 1000
            
            # Create result with issues
            result = CrawlResult(
                target=target_cfg, 
                stats=stats_obj, 
                documents=collected_documents, 
                issues=collected_issues,
                metrics=metrics,
                failed_urls=[{"url": url_str, "reason": f"OSError: {e}"}]
            )
            
            return result, new_links, metrics

        processed_content: Optional[ProcessedContent] = None
        try:
            if not self.content_processor:
                logging.warning(f"Content processor not available for crawler when processing {url_str}.")
                # Create a basic ProcessedContent if no processor
                processed_content = ProcessedContent(
                    url=url_str,
                    title=os.path.basename(file_path) or url_str,
                    content={"text": file_content, "html": "", "formatted_content": ""},  # Basic content
                    metadata={"source": "file", "content_type": "text/plain"},
                    assets={}, structure=[], headings=[], errors=[]
                )
            else:
                # Determine content_type, default to text/html for .html files, else text/plain
                content_type = "text/plain"
                if file_path.lower().endswith((".html", ".htm")):
                    content_type = "text/html"

                processed_content = await self.content_processor.process(
                    content=file_content,
                    url=url_str,
                    content_type=content_type,
                    source_type="file"
                )

            if processed_content:
                doc_dict = {
                    "url": processed_content.url,
                    "title": processed_content.title,
                    "content": processed_content.content,
                    "metadata": processed_content.metadata,
                    "assets": processed_content.assets,
                    "structure": processed_content.structure,
                    "raw_content": file_content,
                    "processed_at": datetime.now(timezone.utc).isoformat()
                }
                collected_documents.append(doc_dict)
                stats_obj.successful_crawls += 1
                stats_obj.bytes_processed += len(file_content.encode('utf-8'))

                # Link extraction
                base_url_for_links = url_info.normalized_url
                if os.path.isfile(file_path):  # Ensure file_path is a file before dirname
                    # Construct a file:// URL for the directory containing the file
                    dir_path = os.path.dirname(file_path)
                    base_url_for_links = create_url_info(f"file://{dir_path}/").normalized_url

                extracted_link_strs = self._find_links_recursive(
                    processed_content.content.get("html", "")  # Use HTML content for link finding
                )

                for link_url_str in extracted_link_strs:
                    try:
                        # Resolve relative links against the file's directory URL
                        link_info = create_url_info(link_url_str, base_url=base_url_for_links)
                        if link_info and self._should_follow_link(link_info, target_cfg, current_depth):
                            new_links.append(link_info)
                    except ValueError as ve:
                        logging.warning(f"Skipping invalid link URL '{link_url_str}' found in {url_str}: {ve}")

                metrics["link_count"] = len(new_links)

                if self.quality_checker and hasattr(self.quality_checker, 'check_quality'):
                    quality_issues, quality_metrics = await self.quality_checker.check_quality(processed_content, raw_content=file_content)
                    if quality_issues:
                        collected_issues.extend(quality_issues)
                    if quality_metrics:  # quality_metrics is a dict
                        metrics.update(quality_metrics)  # Update the main metrics dict
                else:
                    logging.debug(f"Quality checker not available or check_quality not implemented for {url_str}")

            else:  # processed_content is None
                logging.warning(f"Content processing returned None for {url_str}")
                issue = PQ_QualityIssue(
                    issue_type=PQ_IssueType.CONTENT_PROCESSING_ERROR,
                    level=PQ_IssueLevel.WARNING,
                    message=f"Content processor returned no content for {url_str}.",
                    details={"file_path": file_path},
                    url_context=url_str
                )
                collected_issues.append(issue)
                stats_obj.failed_crawls += 1

        except Exception as e:
            logging.error(f"Error processing content for file URL {url_str}: {e}", exc_info=True)
            issue = PQ_QualityIssue(
                issue_type=PQ_IssueType.CONTENT_PROCESSING_ERROR,
                level=PQ_IssueLevel.ERROR,
                message=f"Content processing error for {url_str}: {e}",
                details={"file_path": file_path, "error": str(e), "traceback": traceback.format_exc()},
                url_context=url_str
            )
            collected_issues.append(issue)
            stats_obj.failed_crawls += 1
            stats_obj.errors += 1

        end_time = time.perf_counter()
        metrics["processing_time_ms"] = (end_time - start_time) * 1000

        # Create final result with all collected data
        result = CrawlResult(
            target=target_cfg, 
            stats=stats_obj, 
            documents=collected_documents, 
            issues=collected_issues,
            metrics=metrics,
            processed_url=url_info.normalized_url
        )

        logging.debug(f"Finished processing file URL: {url_str}. Documents: {len(result.documents)}, Issues: {len(result.issues)}, New links: {len(new_links)}")
        return result, new_links, result.metrics

    async def crawl(
        self,
        targets: Union[CrawlTarget, list[CrawlTarget]],
        config: Optional[CrawlerConfig] = None,
    ) -> list[CrawlResult]:
        """Start the crawling process.

        Args:
            targets: A single CrawlTarget or a list of CrawlTarget objects
            config: Optional crawler configuration (overrides default or target-specific config)

        Returns:
            A list of CrawlResult objects for the crawled targets
        """
        if isinstance(targets, CrawlTarget):
            targets = [targets]  # Wrap in list if single target

        results: list[CrawlResult] = []
        for target in targets:
            # Initialize stats for this target
            stats = CrawlStats()

            # Start processing the target URL
            logging.info(f"Crawling {target.url} with depth={target.depth}")
            url_info = create_url_info(target.url)
            
            if url_info.scheme == "file":
                result, _, _ = await self._process_file_url(url_info, 0, target, stats)
                if result:
                    results.append(result)
            else:
                # For non-file URLs, you'd implement HTTP crawling here
                logging.warning(f"Non-file URL crawling not implemented yet: {target.url}")

        return results
